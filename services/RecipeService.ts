import { Recipe, MealType, InstructionType } from '@/components/types';
import { firestoreRepository, FirestoreCollections, PaginationResult } from '@/repositories/firestoreRepository';
import { UnsplashService } from '@/services/UnsplashService';
import { QueryDocumentSnapshot } from 'firebase/firestore';

/**
 * Service for fetching recipes from various sources
 */
export class RecipeService {
  // Cache for storing fetched recipes to avoid redundant network calls
  private static recipeCache = new Map<string, { recipes: Recipe[]; timestamp: number }>();
  private static readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  /**
   * Fetch recipes from Firestore for a specific user with pagination and caching
   *
   * @param userId The user's UID
   * @param pageSize Number of recipes to fetch per page
   * @param lastVisible Last visible document for pagination cursor
   * @param useCache Whether to use cached data if available
   * @returns Paginated result with recipes and pagination metadata
   */
  static async fetchRecipesFromFirestorePaginated(
    userId: string,
    pageSize: number = 10,
    lastVisible?: QueryDocumentSnapshot | null,
    useCache: boolean = true
  ): Promise<PaginationResult<Recipe>> {
    try {
      console.log('Fetching paginated recipes from Firestore for user:', userId, 'pageSize:', pageSize);

      // Check cache for initial load (when lastVisible is null)
      if (useCache && !lastVisible) {
        const cacheKey = `${userId}_recipes`;
        const cached = this.recipeCache.get(cacheKey);
        const now = Date.now();

        if (cached && now - cached.timestamp < this.CACHE_DURATION) {
          console.log('Using cached recipes for user:', userId);
          const startIndex = 0;
          const endIndex = Math.min(pageSize, cached.recipes.length);
          const paginatedRecipes = cached.recipes.slice(startIndex, endIndex);
          const hasMore = endIndex < cached.recipes.length;

          return {
            data: paginatedRecipes,
            lastVisible: hasMore ? ({ id: endIndex.toString() } as QueryDocumentSnapshot) : null,
            hasMore,
          };
        }
      }

      // Fetch paginated recipes from Firestore
      const paginationResult = await firestoreRepository.getPaginatedRecipes(userId, pageSize, lastVisible);

      if (!paginationResult.data || paginationResult.data.length === 0) {
        console.log('No recipes found in Firestore for user:', userId);
        return {
          data: [],
          lastVisible: null,
          hasMore: false,
        };
      }

      console.log(`Found ${paginationResult.data.length} recipes in Firestore`);

      // Transform Firestore recipes to match our Recipe interface
      const transformedRecipes: Recipe[] = await Promise.all(
        paginationResult.data.map(async (recipe: any) => {
          // Generate image URL if not present or if imageQuery is available
          let imageUrl = recipe.imageUrl;
          if (!imageUrl && recipe.imageQuery) {
            try {
              const imageResponse = await UnsplashService.getImageUrl(recipe.imageQuery);
              if (imageResponse.success && imageResponse.imageUrl) {
                imageUrl = imageResponse.imageUrl;
              } else {
                console.warn('Failed to fetch image for recipe:', recipe.title, imageResponse.error);
                imageUrl = 'https://via.placeholder.com/300x200?text=Recipe';
              }
            } catch (error) {
              console.warn('Failed to fetch image for recipe:', recipe.title, error);
              imageUrl = 'https://via.placeholder.com/300x200?text=Recipe';
            }
          } else if (!imageUrl) {
            imageUrl = 'https://via.placeholder.com/300x200?text=Recipe';
          }

          // Transform instructions from Python format to TypeScript format
          let instructions = {
            [InstructionType.HIGH_LEVEL]: 'Instructions not available',
            [InstructionType.DETAILED]: 'Instructions not available',
            [InstructionType.TEACH_MODE]: 'Instructions not available',
          };

          if (recipe.instructions) {
            instructions = {
              [InstructionType.HIGH_LEVEL]:
                recipe.instructions[InstructionType.HIGH_LEVEL] || 'Instructions not available',
              [InstructionType.DETAILED]: recipe.instructions[InstructionType.DETAILED] || 'Instructions not available',
              [InstructionType.TEACH_MODE]:
                recipe.instructions[InstructionType.TEACH_MODE] || 'Instructions not available',
            };
          }

          // Ensure the recipe has all required fields with defaults
          return {
            id: recipe.id || `firestore-${Math.random().toString(36).substring(2, 9)}`,
            title: recipe.title || 'Untitled Recipe',
            timeInMinutes: recipe.timeInMinutes || 30,
            calories: recipe.calories || 400,
            imageUrl,
            compatibleDiets: recipe.compatibleDiets || [],
            ingredients: recipe.ingredients || [],
            instructions,
            mealType: recipe.mealType || MealType.LUNCH,
          };
        })
      );

      // Update cache for initial load
      if (useCache && !lastVisible && transformedRecipes.length > 0) {
        const cacheKey = `${userId}_recipes`;
        this.recipeCache.set(cacheKey, {
          recipes: transformedRecipes,
          timestamp: Date.now(),
        });
      }

      return {
        data: transformedRecipes,
        lastVisible: paginationResult.lastVisible,
        hasMore: paginationResult.hasMore,
      };
    } catch (error) {
      console.error('Error fetching paginated recipes from Firestore:', error);
      return {
        data: [],
        lastVisible: null,
        hasMore: false,
      };
    }
  }

  /**
   * Clear the recipe cache for a specific user or all users
   * @param userId Optional user ID to clear cache for specific user
   */
  static clearCache(userId?: string): void {
    if (userId) {
      const cacheKey = `${userId}_recipes`;
      this.recipeCache.delete(cacheKey);
    } else {
      this.recipeCache.clear();
    }
  }

  /**
   * Fetch recipes from Firestore for a specific user
   *
   * @param userId The user's UID
   * @returns Array of recipes from Firestore, or empty array if none found
   */
  static async fetchRecipesFromFirestore(userId: string): Promise<Recipe[]> {
    try {
      console.log('Fetching recipes from Firestore for user:', userId);

      // Fetch the recipes document for this user
      const recipesDoc = await firestoreRepository.getDocument(FirestoreCollections.GENERATED_RECIPES, userId);

      if (!recipesDoc || !recipesDoc.recipes || !Array.isArray(recipesDoc.recipes)) {
        console.log('No recipes found in Firestore for user:', userId);
        return [];
      }

      const firestoreRecipes = recipesDoc.recipes;
      console.log(`Found ${firestoreRecipes.length} recipes in Firestore`);

      // Transform Firestore recipes to match our Recipe interface
      const transformedRecipes: Recipe[] = await Promise.all(
        firestoreRecipes.map(async (recipe: any) => {
          // Generate image URL if not present or if imageQuery is available
          let imageUrl = recipe.imageUrl;
          if (!imageUrl && recipe.imageQuery) {
            try {
              const imageResponse = await UnsplashService.getImageUrl(recipe.imageQuery);
              if (imageResponse.success && imageResponse.imageUrl) {
                imageUrl = imageResponse.imageUrl;
              } else {
                console.warn('Failed to fetch image for recipe:', recipe.title, imageResponse.error);
                imageUrl = 'https://via.placeholder.com/300x200?text=Recipe';
              }
            } catch (error) {
              console.warn('Failed to fetch image for recipe:', recipe.title, error);
              imageUrl = 'https://via.placeholder.com/300x200?text=Recipe';
            }
          } else if (!imageUrl) {
            imageUrl = 'https://via.placeholder.com/300x200?text=Recipe';
          }

          // Transform instructions from Python format to TypeScript format
          let instructions = {
            [InstructionType.HIGH_LEVEL]: 'Instructions not available',
            [InstructionType.DETAILED]: 'Instructions not available',
            [InstructionType.TEACH_MODE]: 'Instructions not available',
          };

          if (recipe.instructions) {
            instructions = {
              [InstructionType.HIGH_LEVEL]:
                recipe.instructions[InstructionType.HIGH_LEVEL] || 'Instructions not available',
              [InstructionType.DETAILED]: recipe.instructions[InstructionType.DETAILED] || 'Instructions not available',
              [InstructionType.TEACH_MODE]:
                recipe.instructions[InstructionType.TEACH_MODE] || 'Instructions not available',
            };
          }

          // Ensure the recipe has all required fields with defaults
          return {
            id: recipe.id || `firestore-${Math.random().toString(36).substring(2, 9)}`,
            title: recipe.title || 'Untitled Recipe',
            timeInMinutes: recipe.timeInMinutes || 30,
            calories: recipe.calories || 400,
            imageUrl,
            compatibleDiets: recipe.compatibleDiets || [],
            ingredients: recipe.ingredients || [],
            instructions,
            mealType: recipe.mealType || MealType.LUNCH,
          };
        })
      );

      return transformedRecipes;
    } catch (error) {
      console.error('Error fetching recipes from Firestore:', error);
      return [];
    }
  }
}
